import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/widget/no_data_derivatives_order_book.dart';

class ConditionalOrderPage extends StatelessWidget {
  const ConditionalOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: vpColor.backgroundElevation0,
      child: const Center(
        child: Padding(
          padding: EdgeInsets.all(24.0),
          child: NoDataDerivativesOrderBook(),
        ),
      ),
    );
  }
}
