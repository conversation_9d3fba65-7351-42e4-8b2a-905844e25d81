import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

/// Confirmation dialog for cancelling multiple orders
class DerivativesOrderCancelConfirmationDialog extends StatelessWidget {
  /// Number of orders to be cancelled
  final int orderCount;

  /// Callback when user confirms cancellation
  final VoidCallback onConfirm;

  /// Callback when user cancels the action
  final VoidCallback onCancel;

  const DerivativesOrderCancelConfirmationDialog({
    super.key,
    required this.orderCount,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Xác nhận hủy lệnh',
        style: context.textStyle.subtitle16?.copyWith(
          color: vpColor.textPrimary,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning_amber_rounded,
            size: 48,
            color: vpColor.iconAccentRed,
          ),
          const SizedBox(height: 16),
          Text(
            '<PERSON>ạn có chắc chắn muốn hủy $orderCount lệnh đã chọn?',
            style: context.textStyle.body14?.copyWith(
              color: vpColor.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Thao tác này không thể hoàn tác.',
            style: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions: [
        VpsButton.secondarySmall(title: 'Hủy bỏ', onPressed: onCancel),
        VpsButton.primarySmall(title: 'Xác nhận hủy', onPressed: onConfirm),
      ],
    );
  }

  /// Show the confirmation dialog
  static Future<bool?> show(BuildContext context, {required int orderCount}) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => DerivativesOrderCancelConfirmationDialog(
            orderCount: orderCount,
            onConfirm: () => Navigator.of(context).pop(true),
            onCancel: () => Navigator.of(context).pop(false),
          ),
    );
  }
}
