import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/assets.gen.dart';

class DerivativesOrderMultiSelectBottomBar extends StatelessWidget {
  final int selectedCount;

  final VoidCallback onBack;

  final VoidCallback onCancelOrders;

  final bool isLoading;

  const DerivativesOrderMultiSelectBottomBar({
    super.key,
    required this.selectedCount,
    required this.onBack,
    required this.onCancelOrders,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: vpColor.backgroundElevation0,
        boxShadow: [
          BoxShadow(
            color: vpColor.textPrimary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Back button
              Expanded(
                child: VpsButton.primaryXsSmall(
                  title: 'Quay lại',
                  onPressed: isLoading ? null : onBack,
                ),
              ),
              const SizedBox(width: 16),
              // Cancel orders button
              Expanded(
                child: VpsButton.primaryDangerXsSmall(
                  title:
                      selectedCount > 0
                          ? 'Hủy $selectedCount lệnh'
                          : 'Chọn lệnh để hủy',
                  onPressed:
                      selectedCount > 0 && !isLoading
                          ? () => _showMultiOrderCancellationDialog(context)
                          : null,
                  disabled: false,
                  // disabled: selectedCount == 0 || isLoading,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMultiOrderCancellationDialog(BuildContext context) {
    dialogConfirmDeleteMultipleOrders(context, selectedCount, onCancelOrders);
  }
}

void dialogConfirmDeleteMultipleOrders(
  BuildContext context,
  int orderCount,
  VoidCallback onConfirmCallback,
) async {
  VPPopup.outlineAndPrimaryButton(
        title: orderCount > 0 ? 'Huỷ $orderCount lệnh' : 'Hủy tất cả',
        content:
            orderCount > 0
                ? 'Quý khách chắc chắn muốn hủy các lệnh đã chọn?'
                : 'Quý khách chắc chắn muốn hủy tất cả các lệnh?',
      )
      .copyWith(icon: VpTradingAssets.icons.icCancleDialog.svg())
      .copyWith(
        button: VpsButton.secondaryXsSmall(
          title: 'Đóng',
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      )
      .copyWith(
        button: VpsButton.primaryDangerXsSmall(
          title: VPCommonLocalize.current.confirm,
          onPressed: () {
            Navigator.of(context).pop();
            onConfirmCallback();
          },
        ),
      )
      .showDialog(context);
}
