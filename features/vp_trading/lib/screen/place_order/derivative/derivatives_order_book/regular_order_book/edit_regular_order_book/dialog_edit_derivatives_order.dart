import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/order_edit/order_edit_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/enum/market_type.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

import 'derivatives_order_info_section.dart';
import 'derivatives_order_input_section.dart';

/// Shows the derivatives order edit dialog with Cubit-based state management
/// while preserving the original popup dialog UI design
void dialogConfirmEditDerivativesOrders(
  BuildContext context,
  OrderBookModel item, {
  required Function(int quantity, double price) onConfirm,
  VoidCallback? onCancel,
}) async {
  VPPopup.custom(
    padding: const EdgeInsets.all(20),
    child: _DerivativesEditOrderDialog(
      item: item,
      onConfirm: onConfirm,
      onCancel: onCancel,
    ),
  ).showDialog(context);
}

/// Internal dialog widget that uses Cubit-based state management
/// while maintaining the original UI design
class _DerivativesEditOrderDialog extends StatefulWidget {
  final OrderBookModel item;
  final Function(int quantity, double price) onConfirm;
  final VoidCallback? onCancel;

  const _DerivativesEditOrderDialog({
    required this.item,
    required this.onConfirm,
    this.onCancel,
  });

  @override
  State<_DerivativesEditOrderDialog> createState() =>
      _DerivativesEditOrderDialogState();
}

class _DerivativesEditOrderDialogState
    extends State<_DerivativesEditOrderDialog> {
  late OrderEditCubit _orderEditCubit;
  late ValidateOrderCubit _validateOrderCubit;
  late AvailableTradeCubit _availableTradeCubit;
  late StockInfoCubit _stockInfoCubit;

  late TextEditingController _quantityController;
  late TextEditingController _priceController;
  late FocusNode _quantityFocusNode;
  late FocusNode _priceFocusNode;

  int quantity = 0;
  double price = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize values
    quantity = widget.item.qty ?? 0;
    price = double.tryParse(widget.item.price ?? '0') ?? 0.0;

    // Initialize controllers
    _quantityController = TextEditingController(text: quantity.toString());
    _priceController = TextEditingController(text: price.toStringAsFixed(1));
    _quantityFocusNode = FocusNode();
    _priceFocusNode = FocusNode();

    // Initialize Cubits
    _orderEditCubit = OrderEditCubit();
    _validateOrderCubit = ValidateOrderCubit();
    _availableTradeCubit = AvailableTradeCubit();
    _stockInfoCubit = StockInfoCubit();

    // Initialize validation cubit for edit mode
    _validateOrderCubit.setIsOpenByEditOrder(
      true,
      isOddLot: (widget.item.qty ?? 0) < 100,
    );
    _validateOrderCubit.setOrderBookInfo(
      originalVolume: widget.item.qty ?? 0,
      executedVolume: widget.item.execQty ?? 0,
    );

    // Load stock info for derivatives
    _stockInfoCubit.loadData(widget.item.symbol ?? '', isDerivative: true);

    // Load available trade data
    _availableTradeCubit.getAvailableTrade(
      accountId: widget.item.accountId ?? '',
      symbol: widget.item.symbol ?? '',
      quotePrice: widget.item.price,
    );

    // Initialize validation with current values
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _validateOrderCubit.onChangePrice(_priceController.text);
      _validateOrderCubit.onChangeVolumne(_quantityController.text);
    });
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _priceController.dispose();
    _quantityFocusNode.dispose();
    _priceFocusNode.dispose();
    _orderEditCubit.close();
    _validateOrderCubit.close();
    _availableTradeCubit.close();
    _stockInfoCubit.close();
    super.dispose();
  }

  void _updateQuantity(int newQuantity) {
    if (newQuantity >= 0) {
      setState(() {
        quantity = newQuantity;
        _quantityController.text = newQuantity.toString();
        _quantityController.selection = TextSelection.fromPosition(
          TextPosition(offset: _quantityController.text.length),
        );
      });
      // Validate volume change
      _validateOrderCubit.onChangeVolumne(_quantityController.text);
    }
  }

  void _updatePrice(double newPrice) {
    if (newPrice >= 0) {
      setState(() {
        price = newPrice;
        _priceController.text = newPrice.toStringAsFixed(1);
        _priceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _priceController.text.length),
        );
      });
      // Validate price change and reload available trade
      _validateOrderCubit.onChangePrice(_priceController.text);
      _availableTradeCubit.getAvailableTrade(
        accountId: widget.item.accountId ?? '',
        symbol: widget.item.symbol ?? '',
        quotePrice: newPrice.toString(),
      );
    }
  }

  void _handleConfirm() {
    context.pop();
    _orderEditCubit.modifyOrder(
      orderId: widget.item.orderId ?? '',
      accountId: widget.item.accountId ?? '',
      symbol: widget.item.symbol ?? '',
      newPrice: price,
      newVolume: quantity,
      market: MarketType.derivatives.nameServer,
    );
    widget.onConfirm(quantity, price);
  }

  void _handleCancel() {
    context.pop();
    widget.onCancel?.call();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _orderEditCubit),
        BlocProvider.value(value: _validateOrderCubit),
        BlocProvider.value(value: _availableTradeCubit),
        BlocProvider.value(value: _stockInfoCubit),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<OrderEditCubit, OrderEditState>(
            listener: (context, state) {
              if (state.status == OrderEditStatus.success) {
                // Order edit successful - handled by onConfirm callback
              } else if (state.status == OrderEditStatus.failure) {
                // Show error message
                context.showSnackBar(
                  content: state.errorMessage ?? 'Có lỗi xảy ra',
                  snackBarType: VPSnackBarType.error,
                );
              }
            },
          ),
          BlocListener<StockInfoCubit, StockInfoState>(
            listener: (context, state) {
              if (state.stockInfo != null) {
                // Update validation cubit with stock info
                _validateOrderCubit.updateParam(
                  stockInfo: state.stockInfo,
                  action:
                      widget.item.orderTypeEnum == OrderTypeEnum.buy
                          ? OrderAction.buy
                          : OrderAction.sell,
                );
              }
            },
          ),
          BlocListener<AvailableTradeCubit, AvailableTradeState>(
            listener: (context, state) {
              if (state.availableTrade != null) {
                // Update validation cubit with available trade data
                _validateOrderCubit.updateParam(
                  availableTrade: state.availableTrade,
                );
              }
            },
          ),
          BlocListener<ValidateOrderCubit, ValidateOrderState>(
            bloc: _validateOrderCubit,
            listenWhen:
                (previous, current) =>
                    previous.currentPrice != current.currentPrice,
            listener: (context, state) {
              if (state.currentPrice != null) {
                final newPrice = double.tryParse(state.currentPrice!) ?? 0.0;
                if (newPrice != price) {
                  setState(() {
                    price = newPrice;
                    _priceController.text = state.currentPrice!;
                    _priceController.selection = TextSelection.fromPosition(
                      TextPosition(offset: _priceController.text.length),
                    );
                  });
                }
              }
            },
          ),
          BlocListener<ValidateOrderCubit, ValidateOrderState>(
            bloc: _validateOrderCubit,
            listenWhen:
                (previous, current) =>
                    previous.currentVolume != current.currentVolume,
            listener: (context, state) {
              if (state.currentVolume != null) {
                final newQuantity = int.tryParse(state.currentVolume!) ?? 0;
                if (newQuantity != quantity) {
                  setState(() {
                    quantity = newQuantity;
                    _quantityController.text = state.currentVolume!;
                    _quantityController.selection = TextSelection.fromPosition(
                      TextPosition(offset: _quantityController.text.length),
                    );
                  });
                }
              }
            },
          ),
        ],
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              'Sửa lệnh',
              style: context.textStyle.headineBold6?.copyWith(
                color: context.colors.textPrimary,
                fontWeight: FontWeight.w700,
                fontSize: 20,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Information section
            DerivativesOrderInfoSection(item: widget.item),

            // Input fields section
            DerivativesOrderInputSection(
              quantityController: _quantityController,
              priceController: _priceController,
              quantityFocusNode: _quantityFocusNode,
              priceFocusNode: _priceFocusNode,
              onQuantityUpdate: _updateQuantity,
              onPriceUpdate: _updatePrice,
              validateOrderCubit: _validateOrderCubit,
              item: widget.item,
              quantity: quantity,
              price: price,
            ),
            const SizedBox(height: 16),

            // Action buttons section
            BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
              bloc: _validateOrderCubit,
              builder: (context, state) {
                return Row(
                  children: [
                    Expanded(
                      child: VpsButton.secondaryXsSmall(
                        title: 'Đóng',
                        onPressed: _handleCancel,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: VpsButton.primaryXsSmall(
                        title: 'Xác nhận',
                        disabled: !state.isValid,
                        onPressed: state.isValid ? _handleConfirm : null,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
