import 'package:flutter/material.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/derivatives_order_item.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/widget/dialog_cancle_derivatives_order.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/regular_order_book/edit_regular_order_book/dialog_edit_derivatives_order.dart';

class DerivativesOrderList extends StatefulWidget {
  final List<OrderBookModel> items;
  final Future<void> Function() refresh;
  final Future<void> Function() editSuccess;
  final bool? shrinkWrap;
  final bool isMultiSelectMode;
  final bool Function(String)? isOrderSelected;
  final ValueChanged<String>? onSelectionChanged;

  const DerivativesOrderList({
    super.key,
    required this.items,
    required this.refresh,
    this.shrinkWrap = false,
    required this.editSuccess,
    this.isMultiSelectMode = false,
    this.isOrderSelected,
    this.onSelectionChanged,
  });

  @override
  State<DerivativesOrderList> createState() => _DerivativesOrderListState();
}

class _DerivativesOrderListState extends State<DerivativesOrderList> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      shrinkWrap: widget.shrinkWrap ?? false,
      itemCount: widget.items.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final item = widget.items[index];
        return DerivativesOrderItem(
          item: item,
          onTap: () {},
          onEdit: () async {
            // Show edit dialog with new function signature
            dialogConfirmEditDerivativesOrders(
              context,
              item,
              onConfirm: (quantity, price) async {
                // TODO: Implement edit order API call here
                // You can add the API call logic here
                print('Edit order: quantity=$quantity, price=$price');
                await widget.editSuccess();
              },
              onCancel: () {
                // Optional cancel callback
                print('Edit cancelled');
              },
            );
          },
          onCancel: () async {
            dialogConfirmDeleteDerivativesOrders(context, item, () async {
              await widget.refresh();
            });
          },
          isMultiSelectMode: widget.isMultiSelectMode,
          isSelected: widget.isOrderSelected?.call(item.orderId ?? '') ?? false,
          onSelectionChanged: widget.onSelectionChanged,
        );
      },
    );
  }
}
