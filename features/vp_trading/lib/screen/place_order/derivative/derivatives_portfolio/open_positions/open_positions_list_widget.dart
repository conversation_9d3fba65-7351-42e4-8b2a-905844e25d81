import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/positions/open_position_model.dart';
import 'package:vp_trading/cubit/derivative/derivatives_portfolio/derivatives_portfolio_cubit.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_portfolio/open_positions/widget/derivatives_position_item_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_portfolio/widget/no_data_derivatives_portfolio.dart';

class OpenPositionsListWidget extends StatelessWidget {
  const OpenPositionsListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DerivativesPortfolioCubit, DerivativesPortfolioState>(
      builder: (context, state) {
        if (state.isLoading) {
          return _buildLoadingState();
        } else if (state.hasError) {
          return _buildErrorState(
            context,
            state.errorMessage ?? 'Unknown error',
          );
        } else if (state.isSuccess) {
          return _buildSuccessState(context, state.openPositions);
        } else {
          return _buildInitialState(context);
        }
      },
    );
  }

  /// Builds the loading state
  Widget _buildLoadingState() {
    return const Center(child: VPInnerLoading());
  }

  /// Builds the error state with retry functionality
  Widget _buildErrorState(BuildContext context, String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ErrorWithRetryView(
            message: errorMessage,
            onRetry: () {
              context.read<DerivativesPortfolioCubit>().refreshPortfolioData();
            },
          ),
        ],
      ),
    );
  }

  /// Builds the initial state with loading trigger
  Widget _buildInitialState(BuildContext context) {
    // Trigger data loading when in initial state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DerivativesPortfolioCubit>().fetchPortfolioData();
    });

    return _buildLoadingState();
  }

  /// Builds the success state with positions list
  Widget _buildSuccessState(
    BuildContext context,
    List<OpenPositionModel> positions,
  ) {
    if (positions.isEmpty) {
      return const NoDataDerivativesPortfolio();
    }

    return PullToRefreshView(
      onRefresh: () async {
        await context.read<DerivativesPortfolioCubit>().refreshPortfolioData();
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: _buildHeader(context),
          ),
          Expanded(
            child: ListView.separated(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: positions.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final position = positions[index];
                return DerivativesPositionItemWidget(
                  position: position,
                  onSetStopLoss: () => _handleSetStopLoss(context, position),
                  onViewPendingOrders:
                      () => _handleViewPendingOrders(context, position),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the header row matching Figma design
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Mã HĐ/ Lãi dự kiến',
              style: context.textStyle.body14?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: vpColor.textPrimary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'KL/ Giá TB',
              style: context.textStyle.body14?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: vpColor.textPrimary,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 96,
            child: Text(
              'Đóng/Đảo',
              style: context.textStyle.body14?.copyWith(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: vpColor.textPrimary,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// Handles setting stop loss/take profit for a position
  void _handleSetStopLoss(BuildContext context, OpenPositionModel position) {
    // context.read<DerivativesPortfolioCubit>().setStopLossTakeProfit(position);
  }

  /// Handles viewing pending TPSL orders for a position
  void _handleViewPendingOrders(
    BuildContext context,
    OpenPositionModel position,
  ) {
    // context.read<DerivativesPortfolioCubit>().viewPendingTpslOrders(position);
  }
}
